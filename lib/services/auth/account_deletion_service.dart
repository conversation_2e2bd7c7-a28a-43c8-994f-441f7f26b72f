import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:noeji/utils/logger.dart';
import 'package:noeji/utils/nonce_utils.dart';
import 'package:noeji/utils/crypto_utils.dart';

/// Service for handling account deletion with cascading data cleanup
class AccountDeletionService {
  final firebase_auth.FirebaseAuth _auth;
  final FirebaseFirestore _firestore;
  final GoogleSignIn _googleSignIn;

  AccountDeletionService({
    firebase_auth.FirebaseAuth? auth,
    FirebaseFirestore? firestore,
    GoogleSignIn? googleSignIn,
  })  : _auth = auth ?? firebase_auth.FirebaseAuth.instance,
        _firestore = firestore ?? FirebaseFirestore.instance,
        _googleSignIn = googleSignIn ?? GoogleSignIn();

  /// **STAGE 1: Prepare for account deletion by re-authenticating the user.**
  ///
  /// The [showReauthInfoDialogCallback] is a function provided by the UI
  /// to display a dialog informing the user about the re-authentication step.
  /// It should return `true` if the user confirms to proceed, `false` otherwise.
  /// The String argument to the callback is the determined provider name (e.g., "Google", "Apple").
  ///
  /// Returns `true` if re-authentication was successful, `false` otherwise.
  Future<bool> prepareForDeletionAndReauthenticate(
    Future<bool> Function(String providerNameFriendly) showReauthInfoDialogCallback,
  ) async {
    final user = _auth.currentUser;
    if (user == null) {
      Logger.error('prepareForDeletionAndReauthenticate: No user signed in.');
      throw Exception('No user is currently signed in to re-authenticate.');
    }

    final providerId = user.providerData.firstOrNull?.providerId;
    if (providerId == null) {
      Logger.error('prepareForDeletionAndReauthenticate: No provider data found for user ${user.uid}.');
      throw Exception('Cannot determine authentication provider for re-authentication.');
    }

    String providerNameFriendly = "your sign-in provider";
    switch (providerId) {
      case 'google.com':
        providerNameFriendly = "Google";
        break;
      case 'apple.com':
        providerNameFriendly = "Apple";
        break;
      default:
        throw Exception('Unsupported provider for re-authentication: $providerId. Only Google and Apple are supported.');
    }

    // Ask UI to show the informational dialog and get user confirmation
    final bool userConfirmedReauthStep = await showReauthInfoDialogCallback(providerNameFriendly);

    if (!userConfirmedReauthStep) {
      Logger.debug('User cancelled the re-authentication step dialog.');
      return false;
    }

    try {
      Logger.debug('User confirmed. Proceeding with re-authentication for provider: $providerId');
      await _handleReauthentication(user); // This method already throws on failure
      Logger.debug('Re-authentication successful for user: ${user.uid}');
      return true;
    } catch (e) {
      Logger.error('Re-authentication failed during preparation step.', e);
      // Let the UI catch this exception to display a specific error message
      rethrow;
    }
  }

  /// **STAGE 2: Executes the actual account deletion process.**
  ///
  /// This method should ONLY be called after:
  /// 1. `prepareForDeletionAndReauthenticate` has successfully completed.
  /// 2. The UI has received a separate, final confirmation from the user to delete.
  Future<void> executeConfirmedDeletion() async {
    final user = _auth.currentUser;
    if (user == null) {
      Logger.error('executeConfirmedDeletion: No user signed in at the time of confirmed deletion.');
      throw Exception('No user is currently signed in. Deletion cannot proceed.');
    }

    final userId = user.uid;
    Logger.debug('Starting confirmed account deletion for user: $userId');

    try {
      // Step 1: Delete Firestore data FIRST
      // If this fails, we don't want to delete the auth user
      await _deleteUserFirestoreData(userId);
      Logger.debug('Firestore data deletion completed successfully for $userId');

      // Step 2: Delete Firebase Auth user
      // Note: We don't need to handle re-authentication here since it was done in Stage 1
      await _deleteFirebaseAuthUserDirectly(user);
      Logger.debug('Firebase Auth user deletion completed successfully for $userId');

      // Step 3: Sign out explicitly for good measure
      try {
        await _auth.signOut();
        Logger.debug('User signed out after account deletion.');
      } catch (e) {
        Logger.debug('Sign out after deletion failed (this might be expected if user.delete() already handled it): $e');
      }

      Logger.debug('Account deletion process fully completed for $userId.');
    } catch (e) {
      Logger.error('Confirmed account deletion failed for $userId', e);
      rethrow; // To be caught by the UI layer for presentation.
    }
  }

  /// Delete all user data from Firestore using batched writes
  Future<void> _deleteUserFirestoreData(String userId) async {
    final List<WriteBatch> batches = [_firestore.batch()];
    int operationsInBatch = 0;

    void addDeleteToBatch(DocumentReference docRef) {
      if (operationsInBatch >= 499) { // Firestore batch limit is 500, play safe
        batches.add(_firestore.batch());
        operationsInBatch = 0;
      }
      batches.last.delete(docRef);
      operationsInBatch++;
    }

    try {
      Logger.debug('Starting Firestore data deletion for user: $userId');

      // 1. Get all ideabooks for the user
      final ideabooksSnapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('ideabooks')
          .get();

      Logger.debug('Found ${ideabooksSnapshot.docs.length} ideabooks to delete');

      for (final ideabookDoc in ideabooksSnapshot.docs) {
        final ideabookId = ideabookDoc.id;

        // 2a. Get all ideas for this ideabook
        final ideasSnapshot = await _firestore
            .collection('users')
            .doc(userId)
            .collection('ideabooks')
            .doc(ideabookId)
            .collection('ideas')
            .get();

        for (final ideaDoc in ideasSnapshot.docs) {
          addDeleteToBatch(ideaDoc.reference);
        }

        // 2b. Get all notes for this ideabook
        final notesSnapshot = await _firestore
            .collection('users')
            .doc(userId)
            .collection('ideabooks')
            .doc(ideabookId)
            .collection('notes')
            .get();

        for (final noteDoc in notesSnapshot.docs) {
          addDeleteToBatch(noteDoc.reference);
        }

        // 3. Add the ideabook document itself to be deleted
        addDeleteToBatch(ideabookDoc.reference);
      }

      // 4. Add the main user document to be deleted
      final userDocRef = _firestore.collection('users').doc(userId);
      addDeleteToBatch(userDocRef);

      // 5. Commit all batches
      Logger.debug('Committing ${batches.length} batches for Firestore deletion');
      for (final batch in batches) {
        await batch.commit();
      }

      Logger.debug('Firestore data deletion completed successfully');
    } catch (e) {
      Logger.error('Error deleting Firestore data', e);
      throw Exception('Failed to delete user data: $e');
    }
  }

  /// Delete Firebase Auth user directly (assumes re-authentication already done)
  Future<void> _deleteFirebaseAuthUserDirectly(firebase_auth.User user) async {
    try {
      await user.delete();
      Logger.debug('Firebase Auth user deleted successfully');
    } on firebase_auth.FirebaseAuthException catch (e) {
      if (e.code == 'requires-recent-login') {
        // This shouldn't happen since we re-authenticated in Stage 1, but handle it as a fallback
        Logger.error('Unexpected requires-recent-login error after re-authentication in Stage 1');
        throw Exception('Authentication session expired. Please try the deletion process again.');
      } else {
        throw Exception('Could not delete authentication record: ${e.message}');
      }
    } catch (e) {
      throw Exception('An unexpected error occurred while deleting your authentication record: $e');
    }
  }



  /// Handle re-authentication based on the user's sign-in provider
  /// Only supports Google and Apple authentication
  Future<void> _handleReauthentication(firebase_auth.User user) async {
    final providerData = user.providerData;

    if (providerData.isEmpty) {
      throw Exception('No provider data found for re-authentication');
    }

    // Check the provider and handle accordingly
    final providerId = providerData.first.providerId;
    Logger.debug('Handling re-authentication for provider: $providerId');

    switch (providerId) {
      case 'google.com':
        await _reauthenticateWithGoogle(user);
        break;
      case 'apple.com':
        await _reauthenticateWithApple(user);
        break;
      default:
        throw Exception('Unsupported provider for re-authentication: $providerId. Only Google and Apple are supported.');
    }
  }

  /// Re-authenticate with Google
  Future<void> _reauthenticateWithGoogle(firebase_auth.User user) async {
    try {
      Logger.debug('Starting Google re-authentication');
      
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw Exception('Google re-authentication was cancelled');
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = firebase_auth.GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      await user.reauthenticateWithCredential(credential);
      Logger.debug('Google re-authentication successful');
    } catch (e) {
      Logger.error('Google re-authentication failed', e);
      throw Exception('Google re-authentication failed: $e');
    }
  }

  /// Re-authenticate with Apple
  Future<void> _reauthenticateWithApple(firebase_auth.User user) async {
    try {
      Logger.debug('Starting Apple re-authentication');
      
      // Generate raw nonce and its SHA-256 hash
      final rawNonce = NonceUtils.generateNonce();
      final hashedNonce = CryptoUtils.hashPasscode(rawNonce);

      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: hashedNonce,
      );

      final credential = firebase_auth.OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        rawNonce: rawNonce,
      );

      await user.reauthenticateWithCredential(credential);
      Logger.debug('Apple re-authentication successful');
    } catch (e) {
      Logger.error('Apple re-authentication failed', e);
      throw Exception('Apple re-authentication failed: $e');
    }
  }
}
